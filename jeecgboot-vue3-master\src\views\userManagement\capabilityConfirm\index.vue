<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <!-- <j-upload-button type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
<a-button>批量操作
  <Icon icon="mdi:chevron-down"></Icon>
</a-button>
</a-dropdown> -->
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <capabilityConfirmModal @register="registerModal" @success="handleSuccess"></capabilityConfirmModal>
    <capabilityConfirmTableModal @register="registerCapabilityConfirmTableModal" @success="handleSuccess">
    </capabilityConfirmTableModal>

    <!-- 驳回理由弹窗 -->
    <a-modal v-model:visible="rejectModalVisible" :title="rejectModalTitle" @ok="handleRejectConfirm"
      @cancel="handleRejectCancel" width="500px">
      <a-form :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }">
        <a-form-item label="驳回理由" :rules="[{ required: true, message: '请输入驳回理由' }]">
          <a-textarea v-model:value="rejectReason" placeholder="请输入驳回理由" :rows="4" :maxlength="200" show-count />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" name="assContent" setup>
import { ref, computed, unref } from 'vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import capabilityConfirmModal from './modules/capabilityConfirmModal.vue';
import capabilityConfirmTableModal from './modules/capabilityConfirmTableModal.vue';
import { columns, searchFormSchema } from './capabilityConfirm.data';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, returnOne } from './capabilityConfirm.api';
import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';

//注册model
const [registerModal, { openModal }] = useModal();
const [registerCapabilityConfirmTableModal, { openModal: openCapabilityConfirmTableModal }] = useModal();

// 驳回弹窗相关状态
const rejectModalVisible = ref(false);
const rejectModalTitle = ref('');
const rejectReason = ref('');
const currentRejectRecord = ref(null);
const rejectType = ref(''); // 'upAudit' 或 'audit'
//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '人员管理-人员考核内容汇总表',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      // fieldMapToTime: [['timeRange', ['planStartTime', 'planEndTime'], 'YYYY-MM-DD']],
    },
    showTableSetting: false,
    showIndexColumn: true,
    actionColumn: {
      width: 120,
    },
  },
  exportConfig: {
    name: '人员管理-人员考核内容汇总表',
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl,
  },
});

const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
/**
 * 详情
 */
function handleDetailTable(record: Recordable) {
  openCapabilityConfirmTableModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 提交
 */
async function handleSubmit(record) {
  await defHttp.post({
    url: '/lims/employee/onDutyAbilityCommit',
    params: { id: record.id },
  });
  reload();
}
/**
 * 技术审核
 */
async function handleAudit(record) {
  await defHttp.post({
    url: '/lims/employee/onDutyAbilityAuditOrRollBack',
    params: { id: record.id, aduitStatus: 1 },
  });
  reload();
}
/**
 * 提交
 */
async function handleUpAudit(record) {
  await defHttp.post({
    url: '/lims/employee/onDutyAbilityAuditOrRollBack',
    params: { id: record.id },
  });
  reload();
}
/**
 * 提交
 */
async function handleConfirm(record) {
  await defHttp.post({
    url: '/lims/employee/onDutyAbilityCommit',
    params: { id: record.id },
  });
  reload();
}
/**
 * 发起审批驳回
 */
function handleUpAuditReject(record) {
  currentRejectRecord.value = record;
  rejectType.value = 'upAudit';
  rejectModalTitle.value = '发起审批驳回';
  rejectReason.value = '';
  rejectModalVisible.value = true;
}

/**
 * 技术审核驳回
 */
function handleAuditReject(record) {
  currentRejectRecord.value = record;
  rejectType.value = 'audit';
  rejectModalTitle.value = '技术审核驳回';
  rejectReason.value = '';
  rejectModalVisible.value = true;
}

/**
 * 驳回确认
 */
async function handleRejectConfirm() {
  if (!rejectReason.value.trim()) {
    message.error('请输入驳回理由');
    return;
  }
  try {
    const params: any = {
      id: currentRejectRecord.value.id,
      auditStatus : 99
    };
    if (rejectType.value === 'upAudit') {
      params.auditContent = rejectReason.value;
      await defHttp.post({
        url: '/lims/employee/onDutyAbilityAuditOrRollBack',
        params,
      });
    } else if (rejectType.value === 'audit') {
      params.handleAuditReject = rejectReason.value;
      await defHttp.post({
        url: '/lims/employee/onDutyAbilityAuditOrRollBack',
        params,
      });
    }
    rejectModalVisible.value = false;
    reload();
  } catch (error) {
  }
}

/**
 * 驳回取消
 */
function handleRejectCancel() {
  rejectModalVisible.value = false;
  rejectReason.value = '';
  currentRejectRecord.value = null;
}

/**
 * 退回事件
 */
async function handleReturn(record) {
  await returnOne({ id: record.id }, reload);
}
/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, reload);
}
/**
 * 成功回调
 */
function handleSuccess({ isUpdate, values }) {
  reload();
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '编辑',
      onClick: handleEdit.bind(null, record),
    },
  ];
}
/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [
    // {
    //   label: '详情',
    //   onClick: handleDetail.bind(null, record),
    // },
    {
      label: '提交',
      popConfirm: {
        title: '是否确认提交',
        confirm: handleConfirm.bind(null, record),
      },
      ifShow: () => {
        return record.auditStatus == null || record.auditStatus == '' || record.auditStatus == 99;
      },
    },
    {
      label: '同意',
      popConfirm: {
        title: '是否确认同意',
        confirm: handleUpAudit.bind(null, record),
      },
      ifShow: () => {
        return record.auditStatus == '1';
      },
    },
    {
      label: '驳回',
      ifShow: () => {
        return record.auditStatus == '1';
      },
      onClick: handleUpAuditReject.bind(null, record),
    },
    {
      label: '技术审核',
      ifShow: () => {
        return record.auditStatus == '0';
      },
      popConfirm: {
        title: '是否确认技术审核',
        confirm: handleAudit.bind(null, record),
      },
    },
    {
      label: '驳回',
      ifShow: () => {
        return record.auditStatus == '0';
      },
      onClick: handleAuditReject.bind(null, record),
    },
    {
      label: '人员上岗能力确认记录',
      onClick: handleDetailTable.bind(null, record),
    },
    // {
    //   label: '退回',
    //   popConfirm: {
    //     title: '是否确认退回',
    //     confirm: handleReturn.bind(null, record),
    //   },
    //   ifShow: () => {
    //     return record.status == '0';
    //   },
    // },
  ];
}
</script>
<style scoped></style>