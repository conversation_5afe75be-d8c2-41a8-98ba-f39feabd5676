import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { rules } from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';

export const columns: BasicColumn[] = [
  {
    title: '工号',
    dataIndex: 'userCode'
  },
  {
    title: '人员名称',
    dataIndex: 'userName'
  },
  {
    title: '其他授权',
    dataIndex: 'otherAuthorization'
  },
  {
    title: '外部培训内容',
    dataIndex: 'outTrainingContent'
  },
  {
    title: '其他方面',
    dataIndex: 'otherContent'
  },
  
  {
    title: '检测项目',
    dataIndex: 'authorizedContent'
  },
  {
    title: '仪器',
    dataIndex: 'instrumentContent'
  },
  {
    title: '提交人',
    dataIndex: 'commitPerson_dictText'
  },
  {
    title: '提交时间',
    dataIndex: 'commitTime'
  },
  {
    title: '审核人',
    dataIndex: 'auditPerson_dictText'
  },
  {
    title: '审核时间',
    dataIndex: 'auditTime'
  },
  {
    title: '审核意见',
    dataIndex: 'auditContent'
  },
  {
    title: '二次审核人',
    dataIndex: 'assignPerson_dictText'
  },
  {
    title: '二次审核时间',
    dataIndex: 'assignTime'
  },
  {
    title: '二次审核意见',
    dataIndex: 'assignContent'
  },
  {
    title: '审核状态',
    dataIndex: 'auditStatus',
    customRender: ({ text }) => {
      if (text == '0') {
        return '填写完成';
      } else if (text == '1') {
        return '审批完成';
      } else if (text == '2') {
        return '二次审批完成';
      } else if (text == '99') {
        return '驳回';
      } else {
        return text;
      }
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    customRender: ({ text }) => {
      if (text == '0') {
        return '正常';
      } else if (text == '1') {
        return '失效';
      } else {
        return text;
      }
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime'
  },
  {
    title: '创建人',
    dataIndex: 'creator_dictText'
  }
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '姓名',
    field: 'userName',
    component: 'Input'
  },
];

export const formSchema: FormSchema[] = [
  // TODO 主键隐藏字段，目前写死为ID
  { label: '', field: 'id', component: 'Input', show: false },
  {
    label: '车间',
    field: 'workshopName',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
  {
    label: '洁净级别',
    field: 'cleanLevel',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
  {
    label: '检测区域',
    field: 'areaName',
    rules: [{ required: true }, { pattern: /^[^\s]+(\s+[^\s]+)*$/, message: '开头和结尾不能有空格!' }],
    component: 'Input',
  },
];
