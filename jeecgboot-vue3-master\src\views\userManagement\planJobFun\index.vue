<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handlePrint" preIcon="ant-design:plus-outlined"> 打印</a-button>

      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />

      </template>
    </BasicTable>

    <!-- 表单区域 -->
    <editModal @register="registerModal" @success="handleSuccess"></editModal>


  </div>
</template>

<script lang="ts" name="userManagement-externalTraining-index" setup>
import { ref, computed, unref } from 'vue';
import { BasicTable, useTable, TableAction } from '/@/components/Table';
import { useModal } from '/@/components/Modal';
import { useListPage } from '/@/hooks/system/useListPage';
import editModal from './modules/editModal.vue';
import { columns, searchFormSchema } from './PlanJobFun.data';
import { list, deleteOne, batchDelete, getImportUrl, getExportUrl, returnOne } from './PlanJobFun.api';
import { defHttp } from '/@/utils/http/axios';
import { message } from 'ant-design-vue';
import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
import { hiprint } from 'vue-plugin-hiprint';
import panel from './printjs.js'; //模板
//注册model
const [registerModal, { openModal }] = useModal();

// 驳回弹窗相关状态
const rejectModalVisible = ref(false);
const rejectModalTitle = ref('');
const rejectReason = ref('');
const currentRejectRecord = ref(null);
const rejectType = ref(''); // 'upAudit' 或 'audit'

// 上传证书弹窗相关状态
const uploadModalVisible = ref(false);
const certificateUrl = ref('');
const currentUploadRecord = ref(null);
//注册table数据
const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
  tableProps: {
    title: '技术人员岗位授权一览',
    api: list,
    columns,
    canResize: false,
    formConfig: {
      labelWidth: 120,
      schemas: searchFormSchema,
      autoSubmitOnEnter: true,
      showAdvancedButton: true,
      fieldMapToTime: [['timeRange', ['startTime', 'endTime'], 'YYYY-MM-DD']],
    },
    showActionColumn: true,
    showTableSetting: false,
    showIndexColumn: true,
    actionColumn: {
      width: 120,
    },
    pagination: {
      isFetch: false
    },
  },
  exportConfig: {
    name: '技术人员岗位授权一览',
    url: getExportUrl,
  },
  importConfig: {
    url: getImportUrl,
  },
});

const [registerTable, { reload, getDataSource }, { rowSelection, selectedRowKeys }] = tableContext;

/**
 * 新增事件
 */
function handleAdd() {
  openModal(true, {
    isUpdate: false,
    showFooter: true,
  });
}
/**
 * 编辑事件
 */
function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    showFooter: true,
  });
}
function handlePrint() {
  let arr = [{
    tableData: getDataSource().map((item, index) => {
      return {
        ...item,
        index: index + 1,
      }
    })
  }]
  new hiprint.PrintTemplate({ template: panel }).print(arr);
}
/**
 * 删除事件
 */
async function handleDelete(record) {
  await deleteOne({ id: record.id }, reload);
}
/**
 * 退回事件
 */
async function handleReturn(record) {
  await returnOne({ id: record.id }, reload);
}
/**
 * 人员外出培训审批表
 */
function handleSeeSPB(record) {
  openSPBModal(true, {
    record,
    isUpdate: true,
    showFooter: false,
  });
}
/**
 * 批量删除事件
 */
async function batchHandleDelete() {
  await batchDelete({ ids: selectedRowKeys.value }, reload);
}
/**
 *  技术审核
 */
async function handleAudit(record) {
  await defHttp.post({
    url: '/lims/employee/outTrainingAuditOrRollBack',
    params: {
      id: record.id,
      auditStatus: 1
    },
  });
  reload();
}
/**
 * 同意
 */
async function handleUpAudit(record) {
  await defHttp.post({
    url: '/lims/employee/outTrainingAuditOrRollBack',
    params: {
      id: record.id,
      auditStatus: 2
    },
  });
  reload();
}

/**
 * 驳回取消
 */
function handleRejectCancel() {
  rejectModalVisible.value = false;
  rejectReason.value = '';
  currentRejectRecord.value = null;
}

/**
 * 上传证书
 */
function handleUpload(record) {
  currentUploadRecord.value = record;
  certificateUrl.value = '';
  uploadModalVisible.value = true;
}

/**
 * 上传证书确认
 */
async function handleUploadConfirm() {
  if (!certificateUrl.value) {
    message.error('请上传证书文件');
    return;
  }

  try {
    await defHttp.put({
      url: '/lims/employee/uploadOutTrainingData',
      data: {
        id: currentUploadRecord.value.id,
        certificateUrl: certificateUrl.value,
      },
    });

    message.success('证书上传成功');
    uploadModalVisible.value = false;
    reload();
  } catch (error) {
    message.error('证书上传失败');
  }
}

/**
 * 上传证书取消
 */
function handleUploadCancel() {
  uploadModalVisible.value = false;
  certificateUrl.value = '';
  currentUploadRecord.value = null;
}

/**
 * 成功回调
 */
function handleSuccess({ isUpdate, values }) {
  reload();
}
/**
 * 操作栏
 */
function getTableAction(record) {
  return [
    {
      label: '编辑',
      // ifShow: () => {
      //   return record.auditStatus == '0';
      // },
      onClick: handleEdit.bind(null, record),
    },
  ];
}
/**
 * 下拉操作栏
 */
function getDropDownAction(record) {
  return [

  ];
}
</script>
<style scoped></style>
